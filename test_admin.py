#!/usr/bin/env python3
"""
Test Admin Panel functionality
"""

import asyncio
import httpx
import json

API_KEY = "your_super_secret_key_here"
BASE_URL = "http://localhost:8000"

async def test_admin_endpoints():
    """Test all admin endpoints."""
    
    print("🔐 Testing Admin Panel Endpoints")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        
        # Test 1: List profiles (should be empty initially)
        print("\n1️⃣ Testing list profiles...")
        try:
            response = await client.get(
                f"{BASE_URL}/admin/list-profiles/",
                headers={"X-API-KEY": API_KEY}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ List profiles successful")
                print(f"   Total profiles: {result['total']}")
                print(f"   Base path: {result['base_path']}")
                if result['profiles']:
                    for profile in result['profiles']:
                        print(f"   - {profile['name']} ({profile['size']}, {profile['created']})")
            else:
                print(f"❌ List profiles failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ List profiles error: {str(e)}")
        
        # Test 2: Test with wrong API key
        print("\n2️⃣ Testing wrong API key...")
        try:
            response = await client.get(
                f"{BASE_URL}/admin/list-profiles/",
                headers={"X-API-KEY": "wrong_key"}
            )
            
            if response.status_code == 401:
                print("✅ Wrong API key correctly rejected")
            else:
                print(f"❌ Wrong API key should return 401, got {response.status_code}")
                
        except Exception as e:
            print(f"❌ Wrong API key test error: {str(e)}")
        
        # Test 3: Test delete non-existent profile
        print("\n3️⃣ Testing delete non-existent profile...")
        try:
            response = await client.delete(
                f"{BASE_URL}/admin/delete-profile/non_existent_profile",
                headers={"X-API-KEY": API_KEY}
            )
            
            if response.status_code == 200:
                result = response.json()
                if not result['success']:
                    print("✅ Delete non-existent profile correctly handled")
                    print(f"   Message: {result['message']}")
                else:
                    print("❌ Delete non-existent profile should fail")
            else:
                print(f"❌ Delete profile failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Delete profile test error: {str(e)}")
        
        # Test 4: Test admin page access
        print("\n4️⃣ Testing admin page access...")
        try:
            response = await client.get(f"{BASE_URL}/admin")
            
            if response.status_code == 200:
                content = response.text
                if "Admin Panel" in content and "Chrome Profile Management" in content:
                    print("✅ Admin page accessible and contains expected content")
                else:
                    print("❌ Admin page missing expected content")
            else:
                print(f"❌ Admin page failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Admin page test error: {str(e)}")
        
        # Test 5: Test main page links
        print("\n5️⃣ Testing main page...")
        try:
            response = await client.get(f"{BASE_URL}/")
            
            if response.status_code == 200:
                content = response.text
                if "/admin" in content and "/ui" in content and "/docs" in content:
                    print("✅ Main page contains all expected links")
                else:
                    print("❌ Main page missing some links")
            else:
                print(f"❌ Main page failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Main page test error: {str(e)}")

def test_chrome_profiles_directory():
    """Test chrome profiles directory."""
    import os
    
    print("\n6️⃣ Testing chrome profiles directory...")
    
    chrome_profiles_path = os.getenv("CHROME_PROFILE_BASE_PATH", "./chrome_profiles")
    
    if not os.path.exists(chrome_profiles_path):
        print(f"📁 Creating chrome profiles directory: {chrome_profiles_path}")
        os.makedirs(chrome_profiles_path, exist_ok=True)
    
    if os.path.exists(chrome_profiles_path):
        print(f"✅ Chrome profiles directory exists: {chrome_profiles_path}")
        
        # List contents
        try:
            contents = os.listdir(chrome_profiles_path)
            print(f"   Contents: {len(contents)} items")
            for item in contents:
                item_path = os.path.join(chrome_profiles_path, item)
                if os.path.isdir(item_path):
                    print(f"   📁 {item}/")
                else:
                    print(f"   📄 {item}")
        except Exception as e:
            print(f"   ⚠️  Error listing contents: {str(e)}")
    else:
        print(f"❌ Chrome profiles directory not found: {chrome_profiles_path}")

async def main():
    """Main test function."""
    
    # Test directory first
    test_chrome_profiles_directory()
    
    # Test admin endpoints
    await test_admin_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 ADMIN TESTS COMPLETED")
    print("=" * 50)
    
    print("\n💡 Next steps:")
    print("   1. Open http://localhost:8000 to see main page")
    print("   2. Click 'Admin Panel' to access admin interface")
    print("   3. Use API key: your_super_secret_key_here")
    print("   4. Try creating a Chrome profile")
    print("   5. Test profile management features")
    
    print("\n🔧 Admin Panel Features:")
    print("   ✅ Chrome Profile Setup (with browser interaction)")
    print("   ✅ List Chrome Profiles (real-time)")
    print("   ✅ Delete Chrome Profiles")
    print("   ✅ Test Chrome Profiles")
    print("   ✅ API Key Authentication")
    print("   ✅ Error Handling & User Feedback")

if __name__ == "__main__":
    asyncio.run(main())
