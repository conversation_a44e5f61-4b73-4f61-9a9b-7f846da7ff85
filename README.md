# Manus Crawler - FastAPI với Playwright và Realtime Updates

Ứng dụng FastAPI để crawl dữ liệu từ trang web Manus.im sử dụng Playwright, với hỗ trợ Docker, WebSocket realtime updates và quản lý Chrome Profile.

## 🚀 Tính năng

- **FastAPI Backend** với API documentation tự động
- **Playwright Crawler** với khả năng crawl trang web động
- **WebSocket Realtime Updates** cho việc theo dõi tiến trình crawl
- **Chrome Profile Management** cho việc lưu trữ session đăng nhập
- **Docker Support** để triển khai dễ dàng
- **Frontend Interface** đơn giản để tương tác

## 📁 Cấu trúc Dự án

```
manus_crawler_project/
├── Dockerfile
├── docker-compose.yml
├── requirements.txt
├── .env
├── .gitignore
├── README.md
├── Required.md              # Tài liệu chi tiết
├── selectors.py             # CSS selectors
├── crawler.py               # Logic crawl với Playwright
├── main.py                  # Ứng dụng FastAPI chính
├── templates/
│   └── index.html          # Frontend interface
└── chrome_profiles/         # Chrome profiles (sẽ được tạo)
```

## 🛠️ Cài đặt và Chạy

### Phương pháp 1: Sử dụng Docker (Khuyến nghị)

1. **Clone repository và di chuyển vào thư mục:**
   ```bash
   cd manus_crawler_project
   ```

2. **Build và chạy với Docker Compose:**
   ```bash
   docker-compose build
   docker-compose up
   ```

3. **Truy cập ứng dụng:**
   - API Documentation: http://localhost:8000/docs
   - Frontend Interface: http://localhost:8000/ui
   - Health Check: http://localhost:8000/health

### Phương pháp 2: Chạy trực tiếp (Development)

1. **Cài đặt dependencies:**
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

2. **Chạy ứng dụng:**
   ```bash
   python main.py
   ```

## 🎯 Sử dụng

### 1. Frontend Interface

Truy cập http://localhost:8000/ui để sử dụng giao diện web:

- Nhập URL cần crawl (mặc định: https://manus.im/)
- Chọn tên profile Chrome (tùy chọn)
- Chọn chế độ headless/non-headless
- Nhấn "Bắt đầu Crawl Realtime" để bắt đầu
- Theo dõi tiến trình realtime và xem kết quả JSON

### 2. API Endpoints

#### Crawl URL (Realtime)
```bash
curl -X POST "http://localhost:8000/crawl-url-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://manus.im/",
    "request_id": "your-uuid-here",
    "headless": true,
    "profile_name": "my_profile"
  }'
```

#### Crawl HTML Content
```bash
curl -X POST "http://localhost:8000/crawl-html/" \
  -H "Content-Type: application/json" \
  -d '{
    "html_content": "<html>...</html>",
    "headless": true
  }'
```

#### Setup Chrome Profile (Admin)
```bash
curl -X POST "http://localhost:8000/admin/setup-chrome-profile/" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your_super_secret_key_here" \
  -d '{
    "profile_name": "my_manus_profile",
    "url": "https://manus.im/"
  }'
```

### 3. Admin Panel

Truy cập http://localhost:8000/admin để quản lý Chrome profiles:

- **API Key**: `your_super_secret_key_here` (mặc định)
- **Setup Profile**: Mở browser để đăng nhập thủ công
- **List Profiles**: Xem tất cả profiles đã tạo
- **Delete Profile**: Xóa profile không cần thiết
- **Test Profile**: Kiểm tra profile hoạt động

### 4. WebSocket Connection

Kết nối WebSocket để nhận cập nhật realtime:

```javascript
const socket = new WebSocket('ws://localhost:8000/ws/crawl-status/your-request-id');

socket.onmessage = function(event) {
    const message = JSON.parse(event.data);

    if (message.type === 'progress') {
        console.log('Progress:', message.message);
    } else if (message.type === 'data') {
        console.log('Result:', message.data);
    } else if (message.type === 'error') {
        console.log('Error:', message.message);
    }
};
```

## ⚙️ Cấu hình

### Biến môi trường (.env)

```env
ADMIN_API_KEY=your_super_secret_key_here
CHROME_PROFILE_BASE_PATH=./chrome_profiles
PYTHONUNBUFFERED=1
```

### Chrome Profile Management

- **Profile tùy chỉnh**: Sử dụng `profile_name` để tạo/sử dụng profile riêng
- **Profile hệ thống**: Sử dụng `use_system_profile=true` để dùng profile Chrome hiện tại
- **Profile tạm thời**: Không chỉ định profile nào (mặc định)

## 🔧 Phát triển

### Cập nhật Selectors

Chỉnh sửa file `selectors.py` để cập nhật CSS selectors khi cấu trúc HTML của Manus.im thay đổi.

### Thêm tính năng Crawl

Chỉnh sửa file `crawler.py` để thêm logic crawl mới hoặc cải thiện hiệu suất.

### Mở rộng API

Thêm endpoints mới trong `main.py` để hỗ trợ các tính năng bổ sung.

## 📚 Tài liệu chi tiết

Xem file `Required.md` để có hướng dẫn chi tiết về:
- Cách xác định CSS selectors
- Quản lý Chrome profiles trong Docker
- Xử lý đăng nhập và xác thực
- Best practices cho crawling
- Troubleshooting

## 🚨 Lưu ý quan trọng

1. **Đạo đức Crawling**: Tuân thủ robots.txt và không spam requests
2. **Bảo mật**: Thay đổi `ADMIN_API_KEY` trong production
3. **Rate Limiting**: Thêm delay giữa các requests nếu cần
4. **Legal Compliance**: Đảm bảo tuân thủ terms of service của trang web

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **Playwright browser not found**: Chạy `playwright install chromium`
2. **Permission denied**: Kiểm tra quyền truy cập thư mục chrome_profiles
3. **WebSocket connection failed**: Kiểm tra firewall và port 8000
4. **Selectors not working**: Cập nhật selectors trong `selectors.py`

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
