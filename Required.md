
# Xây dựng Ứng dụng FastAPI Crawl Dữ liệu Manus.im v<PERSON><PERSON> Playwright, Docker và Realtime Frontend

Tài liệu này cung cấp hướng dẫn toàn diện để xây dựng một ứng dụng FastAPI mạnh mẽ cho việc crawl dữ liệu từ trang web Manus.im. Ứng dụng sẽ được đóng gói bằng Docker, sử dụng Playwright để tương tác trình duy<PERSON>, cung cấp API để điều khiển, và hỗ trợ cập nhật realtime cho frontend qua WebSockets. <PERSON><PERSON><PERSON><PERSON> ra, còn có tính năng cho phép admin cấu hình Chrome Profile.

## Mục Lục

1.  [Thiết lập và Quản lý với Docker](#1-thiết-lập-và-quản-lý-với-docker)
    *   [Tại sao dùng Docker?](#tại-sao-dùng-docker)
    *   [File `Dockerfile`](#file-dockerfile)
    *   [File `requirements.txt`](#file-requirementstxt)
    *   [File `docker-compose.yml`](#file-docker-composeyml)
    *   [Build và Chạy Docker Container](#build-và-chạy-docker-container)
2.  [Xây dựng Backend với FastAPI](#2-xây-dựng-backend-với-fastapi)
    *   [Cấu trúc Thư mục Dự án](#cấu-trúc-thư-mục-dự-án)
    *   [Định nghĩa Selectors (`selectors.py`)](#định-nghĩa-selectors-selectorspy)
    *   [Logic Crawl với Playwright (`crawler.py`)](#logic-crawl-với-playwright-crawlerpy)
    *   [Ứng dụng FastAPI Chính (`main.py`)](#ứng-dụng-fastapi-chính-mainpy)
3.  [Những Lưu Ý Quan Trọng Khi Phát Triển](#3-những-lưu-ý-quan-trọng-khi-phát-triển)
    *   [Crawl Trang Web Động và Xác thực](#crawl-trang-web-động-và-xác-thực)
    *   [Tính Ổn định của Selectors](#tính-ổn-định-của-selectors)
    *   [Đạo đức Crawling](#đạo-đức-crawling)
    *   [Bảo mật Endpoint Admin](#bảo-mật-endpoint-admin)
    *   [Realtime WebSocket và Xử lý Nhiều Request](#realtime-websocket-và-xử-lý-nhiều-request)
    *   [Quản lý Chrome Profile trong Docker](#quản-lý-chrome-profile-trong-docker)
    *   [Chạy Playwright với Giao diện (UI) trong Docker](#chạy-playwright-với-giao-diện-ui-trong-docker)
    *   [Xử lý Lỗi và Timeout](#xử-lý-lỗi-và-timeout)
4.  [Xây dựng Frontend Đơn giản với Cập nhật Realtime](#4-xây-dựng-frontend-đơn-giản-với-cập-nhật-realtime)
    *   [Mục đích](#mục-đích)
    *   [Nội dung file `templates/index.html`](#nội-dung-file-templatesindexhtml)
    *   [Cách JavaScript hoạt động](#cách-javascript-hoạt-động)

---

## 1. Thiết lập và Quản lý với Docker

Docker giúp đóng gói ứng dụng cùng tất cả các dependencies của nó vào một container, đảm bảo tính nhất quán trên các môi trường khác nhau.

### Tại sao dùng Docker?
*   **Môi trường nhất quán:** Loại bỏ lỗi "works on my machine".
*   **Quản lý dependencies dễ dàng:** Tất cả thư viện (Python, Playwright browsers) được cài đặt trong image.
*   **Triển khai đơn giản:** Dễ dàng deploy lên các server hoặc cloud platform.
*   **Cô lập:** Ứng dụng chạy trong môi trường riêng, không ảnh hưởng đến hệ thống host.

### File `Dockerfile`
File này mô tả cách xây dựng Docker image cho ứng dụng.

```dockerfile
# Dockerfile
FROM mcr.microsoft.com/playwright/python:v1.44.0-jammy
# Sử dụng image chính thức của Playwright để có sẵn trình duyệt và dependencies.
# Kiểm tra phiên bản mới nhất tại: https://playwright.dev/python/docs/docker

WORKDIR /app

# Sao chép file requirements trước để tận dụng Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Sao chép toàn bộ mã nguồn ứng dụng
COPY . .

# Biến môi trường (ví dụ)
ENV PYTHONUNBUFFERED=1
ENV CHROME_PROFILE_BASE_PATH=/app/chrome_profiles # Đường dẫn profile trong container

# Mở port cho FastAPI
EXPOSE 8000

# Lệnh để chạy ứng dụng FastAPI
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### File `requirements.txt`
Liệt kê các thư viện Python cần thiết.

```txt
# requirements.txt
fastapi
uvicorn[standard] # [standard] bao gồm hỗ trợ websockets
playwright
pydantic
httpx
# jinja2 # Thêm nếu bạn dùng Jinja2Templates cho frontend
```

### File `docker-compose.yml`
Giúp định nghĩa và chạy các multi-container Docker applications. Ở đây, chúng ta dùng nó để quản lý service và volume dễ dàng hơn.

```yaml
# docker-compose.yml
version: '3.8'

services:
  web_crawler_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: manus_crawler_service
    ports:
      - "8000:8000" # Ánh xạ port 8000 của container ra port 8000 của máy host
    volumes:
      - .:/app # Mount code hiện tại vào /app (tiện cho development, live reload)
      - chrome_profiles_data:/app/chrome_profiles # Volume để lưu trữ Chrome Profiles
    environment:
      # ADMIN_API_KEY: "your_super_secret_key_here" # Đặt API key ở đây hoặc trong .env file
      # DISPLAY: host.docker.internal:0 # Có thể cần cho X11 forwarding trên một số hệ thống
      # PLAYWRIGHT_BROWSERS_PATH: /ms-playwright # Thường đã được set trong image Playwright
      PYTHONUNBUFFERED: 1
      CHROME_PROFILE_BASE_PATH: /app/chrome_profiles # Đảm bảo giống với Dockerfile
    # Để Playwright hiển thị UI (non-headless) từ Docker ra máy host,
    # cần cấu hình X11 forwarding hoặc dùng VNC. Việc này phức tạp và phụ thuộc vào HĐH host.
    # Cho development, chạy Playwright UI trực tiếp trên máy host thường dễ hơn.

volumes:
  chrome_profiles_data: # Tạo một named volume để lưu trữ dữ liệu profile Chrome
```

### Build và Chạy Docker Container

1.  **Build image:** (Trong thư mục gốc của dự án, nơi có `Dockerfile` và `docker-compose.yml`)
    ```bash
    docker-compose build
    ```
    Hoặc nếu không dùng docker-compose:
    ```bash
    docker build -t manus_crawler_app .
    ```

2.  **Chạy container:**
    ```bash
    docker-compose up
    ```
    Hoặc nếu không dùng docker-compose:
    ```bash
    # Tạo volume nếu chưa có: docker volume create chrome_profiles_data
    docker run -d -p 8000:8000 \
      -v $(pwd):/app \
      -v chrome_profiles_data:/app/chrome_profiles \
      -e ADMIN_API_KEY="your_super_secret_key_here" \
      -e CHROME_PROFILE_BASE_PATH="/app/chrome_profiles" \
      --name manus_crawler_instance \
      manus_crawler_app
    ```
    (Trên Windows PowerShell, `$(pwd)` có thể cần thay bằng `${PWD}` hoặc đường dẫn tuyệt đối.)

Ứng dụng FastAPI sẽ chạy và có thể truy cập tại `http://localhost:8000`.

---

## 2. Xây dựng Backend với FastAPI

FastAPI là một web framework hiện đại, hiệu suất cao để xây dựng API với Python.

### Cấu trúc Thư mục Dự án
```
manus_crawler_project/
├── Dockerfile
├── docker-compose.yml
├── requirements.txt
├── html-manus.example.html   # File HTML mẫu (nếu dùng)
├── selectors.py              # CSS selectors
├── crawler.py                # Logic crawl với Playwright
├── main.py                   # Ứng dụng FastAPI
├── templates/                  # (Tùy chọn) Cho frontend HTML
│   └── index.html
└── chrome_profiles/            # (Sẽ được tạo) Nơi lưu trữ các Chrome profile
```

Bạn nói đúng! Tôi đã tập trung vào việc sắp xếp lại cấu trúc tổng thể và quên đi sâu vào chi tiết của phần "Định nghĩa Selectors". Xin lỗi về thiếu sót đó.

Dưới đây là bản cập nhật cho phần **Định nghĩa Selectors (`selectors.py`)** trong file Markdown, với hướng dẫn chi tiết hơn và ví dụ cụ thể dựa trên file `html-manus.example.html` bạn đã cung cấp.

---

**Cập nhật vào file Markdown (phần Selectors):**

```markdown

### Định nghĩa Selectors (`selectors.py`)

File `selectors.py` là nơi tập trung tất cả các CSS selectors (hoặc XPath selectors) được sử dụng để xác định vị trí các phần tử HTML mà chúng ta muốn trích xuất dữ liệu hoặc tương tác. Việc tách riêng selectors ra một file giúp code chính (`crawler.py`) gọn gàng hơn và dễ dàng cập nhật selectors khi cấu trúc HTML của trang web mục tiêu thay đổi.

#### Cách xác định Selectors chi tiết

1.  **Mở File HTML hoặc Trang Web:**
    *   Nếu bạn có file HTML tĩnh (như `html-manus.example.html`), mở nó bằng trình duyệt.
    *   Nếu bạn crawl trang web live, mở URL đó trong trình duyệt.

2.  **Sử dụng Developer Tools (F12):**
    *   Nhấn phím `F12` (hoặc click chuột phải -> "Inspect" / "Kiểm tra") để mở công cụ dành cho nhà phát triển của trình duyệt.
    *   Chuyển sang tab "Elements" (hoặc "Inspector").

3.  **Chọn Phần tử Cần Lấy Dữ liệu:**
    *   Sử dụng công cụ "Select an element" (thường có biểu tượng con trỏ chuột hoặc hình vuông có con trỏ) trong Developer Tools để click trực tiếp vào phần tử trên trang mà bạn muốn lấy dữ liệu (ví dụ: tiêu đề bài viết, một đoạn văn, một icon).
    *   Developer Tools sẽ tự động highlight mã HTML tương ứng của phần tử đó trong tab "Elements".

4.  **Phân tích Thuộc tính và Cấu trúc HTML của Phần tử:**
    *   **`id`**: Nếu phần tử có `id` (ví dụ: `<div id="main-content">`), đây thường là selector tốt nhất vì ID được thiết kế để là duy nhất trên trang. Selector: `#main-content`.
    *   **`class`**: Phần tử có thể có một hoặc nhiều class (ví dụ: `<span class="task-title important">`).
        *   Nếu một class đủ đặc trưng: `.task-title`.
        *   Nếu cần kết hợp nhiều class: `.task-title.important` (chọn phần tử có cả hai class).
        *   **Cẩn trọng với class động/utility:** Các class như `flex`, `w-full`, `text-sm`, `bg-[var(--...)]` thường là các utility class (ví dụ từ Tailwind CSS) và có thể xuất hiện ở nhiều nơi hoặc dễ thay đổi. Hạn chế dùng chúng làm selector chính trừ khi không có lựa chọn tốt hơn hoặc khi kết hợp với các selector ổn định khác.
    *   **Thuộc tính `data-*`**: Các thuộc tính tùy chỉnh bắt đầu bằng `data-` (ví dụ: `<div data-event-id="OZXIxaFvk9I5cn63HJKzZN">`) thường được thêm vào bởi JavaScript framework hoặc lập trình viên và có thể rất ổn định. Selector: `div[data-event-id="OZXIxaFvk9I5cn63HJKzZN"]` (chính xác) hoặc `div[data-event-id]` (bất kỳ div nào có thuộc tính này).
    *   **Các thuộc tính HTML tiêu chuẩn:** `href` cho thẻ `<a>`, `src` cho thẻ `<img>`, `placeholder` cho `input`, `aria-label`, etc. Ví dụ: `input[placeholder="Send message to Manus"]`.
    *   **Cấu trúc Thẻ (Quan hệ Cha-Con, Anh-Em):**
        *   `div > span`: Chọn `span` là con trực tiếp của `div`.
        *   `article p`: Chọn tất cả `p` là con cháu (không nhất thiết trực tiếp) của `article`.
        *   `h2 + p`: Chọn `p` ngay sau (anh em liền kề) một thẻ `h2`.
    *   **Nội dung Text (Playwright-specific):** Playwright cho phép chọn phần tử dựa trên nội dung text của nó.
        *   `button:has-text("New task")`: Chọn `button` có chứa đoạn text "New task".
        *   `text="Some exact text"`: Chọn phần tử có text chính xác là "Some exact text".

5.  **Kiểm tra và Tinh chỉnh Selector:**
    *   Trong Console của Developer Tools, bạn có thể kiểm tra selector bằng cách sử dụng:
        *   `$$("your-css-selector")` (trả về một mảng các phần tử khớp).
        *   `$x("your-xpath-selector")` (cho XPath).
    *   Xem số lượng phần tử trả về. Nếu có nhiều hơn 1 và bạn chỉ muốn 1, hãy làm cho selector của bạn cụ thể hơn.
    *   Đảm bảo selector không quá cứng nhắc (ví dụ: phụ thuộc vào quá nhiều class có thể thay đổi) nhưng cũng không quá chung chung (trả về quá nhiều kết quả không mong muốn).

#### Nội dung file `selectors.py` (Chi tiết hơn với ví dụ từ `html-manus.example.html`)

```python
# selectors.py

# --- General Page Elements ---
PAGE_TITLE_TAG = "title" # Lấy thẻ <title> của trang

# --- Sidebar (Task List / Chat History) ---
# Mỗi mục trong danh sách các cuộc trò chuyện/task ở sidebar
# Quan sát: mỗi item có class 'group', 'flex', 'h-14' và là con của div.px-2
# data-event-id không có ở đây, nên dùng cấu trúc class
TASK_ITEM_CONTAINER_CSS = "div.flex.flex-col.overflow-auto.pt-2.pb-5 > div.px-2 > div.group.flex.h-14"
# Bên trong mỗi TASK_ITEM_CONTAINER_CSS:
# Icon của task/chat
TASK_ITEM_ICON_IMG_CSS = "img" # Thường là img duy nhất trong div con đầu tiên
# Tiêu đề của task/chat (lấy cả text và thuộc tính 'title')
TASK_ITEM_TITLE_SPAN_CSS = "span.truncate.text-sm.font-medium[title]" # Tìm span có các class này VÀ có thuộc tính title
# Thời gian của task/chat (khá khó vì class CSS có vẻ động)
# Ví dụ: "14:46" - nằm trong span có class 'text-[var(--text-tertiary)] text-xs whitespace-nowrap'
# Class này rất chung chung, ta có thể dựa vào vị trí tương đối với title_span
# Hoặc nếu có cấu trúc cố định hơn: ví dụ, nó là span thứ 2 trong div chứa title và timestamp
TASK_ITEM_TIMESTAMP_CSS = "span.text-\\[var\\(--text-tertiary\\)\\].text-xs.whitespace-nowrap"
# Preview của task/chat (lấy thuộc tính 'title')
TASK_ITEM_PREVIEW_SPAN_CSS = "span.min-w-0.flex-1.truncate.text-xs[title]"

# Nút "New task"
# Quan sát: button chứa text 'New task' và có các SVG icon.
# Playwright selector sẽ hiệu quả hơn ở đây.
NEW_TASK_BUTTON_PLAYWRIGHT = "button:has-text('New task')"
# Hoặc nếu có class ổn định hơn:
# NEW_TASK_BUTTON_CSS = "button.flex.min-w-\\[36px\\]" # (Cần kiểm tra kỹ vì class này có thể dùng ở nhiều nơi)

# --- Main Content Area (Chat View) ---
# Tiêu đề của task/chat đang hiển thị ở phần nội dung chính
CURRENT_TASK_TITLE_MAIN_CSS = "div.sticky.top-0 div.text-\\[var\\(--text-primary\\)\\].text-lg.font-medium span.whitespace-nowrap.text-ellipsis.overflow-hidden"

# Container cho mỗi "event" hoặc "message" trong luồng chat
# Các div này có thuộc tính data-event-id rất hữu ích
CHAT_EVENT_CONTAINER_CSS = "div[data-event-id]"
# Bên trong mỗi CHAT_EVENT_CONTAINER_CSS:
# Tin nhắn của User: thường nằm trong div có style bo tròn đặc biệt
# Ví dụ: <div class="relative flex items-center rounded-[12px] ... p-3 ltr:rounded-br-none ...">
# Selector này cần cụ thể hơn để phân biệt với tin nhắn của Manus.
# Giả định: tin nhắn user có class "items-end" ở container cha và text nằm trong div con có các class p-3,...
USER_MESSAGE_TEXT_CSS = "div.items-end div.p-3.ltr\\:rounded-br-none span.u-break-words"
# Tin nhắn của Manus: thường nằm trong div có class 'prose' để định dạng Markdown
MANUS_MESSAGE_CONTENT_PROSE_CSS = "div.prose"
# Thời gian của mỗi tin nhắn (ban đầu invisible, chỉ hiện khi hover group)
# Đây là một thách thức nếu chỉ parse HTML tĩnh. Playwright có thể hover để lấy.
# Nếu lấy từ HTML tĩnh, ta tìm div có class float-right và text-[12px]
MESSAGE_TIMESTAMP_CSS = "div.float-right.text-\\[12px\\]"

# File đính kèm trong tin nhắn của Manus (ví dụ: file code)
# Thường nằm trong div có class `rounded-[10px] bg-[var(--fill-tsp-white-main)] group/attach`
ATTACHMENT_IN_MESSAGE_CONTAINER_CSS = "div.rounded-\\[10px\\].bg-\\[var\\(--fill-tsp-white-main\\)\\].group\\/attach"
ATTACHMENT_IN_MESSAGE_FILENAME_CSS = f"{ATTACHMENT_IN_MESSAGE_CONTAINER_CSS} div.text-sm.text-\\[var\\(--text-primary\\)\\]"
ATTACHMENT_IN_MESSAGE_DETAILS_CSS = f"{ATTACHMENT_IN_MESSAGE_CONTAINER_CSS} div.text-xs.text-\\[var\\(--text-tertiary\\)\\]"

# --- Footer Sidebar ---
# Avatar của người dùng ở footer
FOOTER_USER_AVATAR_IMG_CSS = "footer img.w-full.h-full.object-cover"
# Tên người dùng ở footer
FOOTER_USER_NAME_SPAN_CSS = "footer span.text-sm.leading-5.font-medium.text-\\[var\\(--text-primary\\)\\]"

# --- Computer Preview Area (khi xem file đính kèm chi tiết) ---
# Selector cho khu vực "computer" preview (nếu có)
COMPUTER_PREVIEW_CONTAINER_ID = "#computer" # Giả sử có id="computer"
# Tên file trong header của computer preview
COMPUTER_PREVIEW_FILENAME_CSS = f"{COMPUTER_PREVIEW_CONTAINER_ID} div.text-\\[var\\(--text-tertiary\\)\\]" # Cần cụ thể hơn
# Nội dung code trong Monaco Editor (rất khó lấy chính xác bằng CSS tĩnh)
# Cần dùng Playwright API để tương tác với Monaco Editor nếu muốn lấy chuẩn
# Selector tĩnh chỉ có thể lấy text chung, không phân biệt dòng code.
COMPUTER_PREVIEW_MONACO_LINES_CSS = f"{COMPUTER_PREVIEW_CONTAINER_ID} div.monaco-editor div.view-lines"

# --- Input Area ---
# Textarea để nhập tin nhắn
CHAT_INPUT_TEXTAREA_CSS = "textarea[placeholder='Send message to Manus']"
# Nút gửi tin nhắn (thường là SVG, không có text, cần tìm thuộc tính như aria-label hoặc class/id ổn định)
# Ví dụ (cần kiểm tra): SEND_MESSAGE_BUTTON_CSS = "button[aria-label='Send']"
# Hoặc nếu nó là button cuối cùng trong một div cụ thể:
# SEND_MESSAGE_BUTTON_CSS = "div.px-3.flex.gap-2.item-center > div.ml-auto > button" (Rất dễ thay đổi)

# --- Login Page (Nếu cần tự động hóa đăng nhập) ---
# Các selector này rất quan trọng và cần chính xác tuyệt đối cho trang live
# Nút "Sign in with Google"
SIGN_IN_WITH_GOOGLE_BUTTON_XPATH = "//button[.//div[contains(text(),'Google')]]"
# Hoặc SIGN_IN_WITH_GOOGLE_BUTTON_CSS = "button:has-text('Sign in with Google')" # (Kiểm tra text chính xác)
# Input email trên trang đăng nhập Google
GOOGLE_EMAIL_INPUT_SELECTOR = "input[type='email']"
# Input password trên trang đăng nhập Google
GOOGLE_PASSWORD_INPUT_SELECTOR = "input[type='password']"
# Nút "Next" trên trang đăng nhập Google
GOOGLE_NEXT_BUTTON_SELECTOR = "button:has-text('Next')" # Hoặc selector khác ổn định hơn
```

**Giải thích các điểm quan trọng trong `selectors.py` (cập nhật):**
*   **Sử dụng cả CSS và XPath/Playwright-specific selectors:** CSS selector phổ biến và dễ đọc. XPath mạnh mẽ hơn cho các cấu trúc phức tạp. Playwright selectors (`:has-text()`) rất hữu ích.
*   **Cụ thể hóa selectors:**
    *   `span.truncate.text-sm.font-medium[title]`: Thêm `[title]` để đảm bảo chỉ chọn `span` có thuộc tính `title`, giúp phân biệt với các `span` khác có cùng class.
    *   `div[data-event-id]`: Rất tốt vì `data-event-id` có vẻ là một định danh ổn định.
*   **Escape ký tự đặc biệt:** Các ký tự như `[`, `]`, `(`, `)`, `--` trong giá trị class (thường thấy ở Tailwind CSS) cần được escape bằng `\\` khi viết CSS selector. Ví dụ: `text-\\[var\\(--text-tertiary\\)\\]`.
*   **Ghi chú về sự ổn định:** Luôn ghi chú lại nếu một selector có vẻ không ổn định hoặc phụ thuộc vào các class dễ thay đổi.
*   **Selectors cho hành động:** Đối với các nút bấm không có text rõ ràng (ví dụ: nút gửi tin nhắn bằng icon), bạn cần tìm các thuộc tính như `aria-label`, `data-testid`, hoặc một `id` / `class` duy nhất.
*   **Login Page Selectors:** Đây là phần cực kỳ quan trọng nếu bạn muốn tự động hóa đăng nhập. Các selector này phải chính xác với trang đăng nhập của Google và Manus. Trang Google thay đổi thường xuyên, nên các selector này cần được kiểm tra kỹ.

Sau khi có `selectors.py` chi tiết, bạn có thể sử dụng chúng trong `crawler.py` để trích xuất dữ liệu.

# ... (Các phần còn lại của file Markdown như đã hướng dẫn) ...

```

### Logic Crawl với Playwright (`crawler.py`)
File này chứa các hàm để khởi chạy Playwright, tương tác với trang, và trích xuất dữ liệu.
*(Nội dung file `crawler.py` đã được cập nhật ở câu trả lời trước, bao gồm `get_system_chrome_user_data_dir`, `launch_browser_with_profile`, `setup_chrome_profile_interactive`, và hàm `crawl_manus_page_content` đã được nâng cấp.)*

**Điểm chính trong `crawler.py` (đã cập nhật):**
*   **`CHROME_PROFILE_BASE_PATH`**: Sử dụng biến môi trường.
*   **`launch_browser_with_profile()`**: Hỗ trợ chạy với profile Chrome tùy chỉnh (`profile_name`), profile hệ thống (`use_system_profile`), hoặc profile tạm thời. Có tùy chọn `headless`.
*   **`setup_chrome_profile_interactive()`**: Mở trình duyệt (non-headless) cho phép người dùng đăng nhập thủ công vào Manus, lưu session vào profile được chỉ định.
*   **`crawl_manus_page_content()`**: Hàm crawl chính, có khả năng:
    *   Nhận `html_content` (để parse file tĩnh) hoặc `url` (để crawl trang live).
    *   Sử dụng `profile_name`, `use_system_profile`, `headless`.
    *   Chấp nhận `websocket_callback` để gửi cập nhật tiến trình.
    *   Trả về dictionary `data` chứa thông tin đã crawl và trạng thái profile.

### Ứng dụng FastAPI Chính (`main.py`)
Đây là trung tâm của ứng dụng, định nghĩa các API endpoints, Pydantic models, và logic WebSocket.
*(Nội dung file `main.py` đã được cập nhật ở câu trả lời trước, bao gồm Pydantic Models, `ConnectionManager` cho WebSocket, các endpoint API đã được nâng cấp, và endpoint `/admin/setup-chrome-profile/` với bảo mật API Key cơ bản.)*

**Điểm chính trong `main.py` (đã cập nhật):**
*   **Pydantic Models:** `TaskItem`, `ChatMessage`, `FooterUserInfo`, `AttachmentPreview`, `ProfileStatus`, `CrawledDataResponse`, `CrawlUrlRequestWithId`, `CrawlByIdRequestWithId`, `SetupProfileRequest`, `WebSocketMessage`.
*   **`ConnectionManager`**: Quản lý các kết nối WebSocket, đảm bảo tin nhắn được gửi đến đúng client dựa trên `request_id`.
*   **WebSocket Endpoint (`/ws/crawl-status/{request_id}`)**: Client kết nối tới đây để nhận cập nhật realtime cho một request crawl cụ thể.
*   **Admin Endpoint (`/admin/setup-chrome-profile/`)**:
    *   Được bảo vệ bằng API Key (`X-API-KEY` header).
    *   Gọi `setup_chrome_profile_interactive()` từ `crawler.py`.
    *   **Yêu cầu tương tác người dùng** nếu chạy với UI.
*   **Crawl Endpoints (ví dụ: `/crawl-url-realtime/`, `/crawl-task-by-id-realtime/`)**:
    *   Nhận `request_id` trong body (model `CrawlRequestWithId` hoặc `CrawlByIdRequestWithId`).
    *   Truyền `websocket_callback` (sử dụng `manager.send_to_request_id`) vào hàm crawl.
    *   Hỗ trợ `profile_name`, `use_system_profile`, `headless`.
*   **Endpoint hiển thị (`/ui` và `/view-crawled-data/`)**: Cung cấp cách đơn giản để tương tác và xem kết quả.

---

## 3. Những Lưu Ý Quan Trọng Khi Phát Triển

### Crawl Trang Web Động và Xác thực
*   **Chờ đợi Phần tử Động:** Manus.im là một ứng dụng web hiện đại, nội dung có thể được tải bằng JavaScript. Luôn sử dụng các cơ chế đợi của Playwright:
    *   `page.wait_for_load_state('networkidle')` (chờ mạng không còn hoạt động nhiều).
    *   `page.wait_for_selector(selector, state='visible', timeout=ms)` (chờ một selector cụ thể xuất hiện).
    *   `page.wait_for_timeout(ms)` (chờ một khoảng thời gian cố định - nên hạn chế dùng).
*   **Xử lý Đăng nhập:**
    *   **Setup Profile Thủ công:** Endpoint `/admin/setup-chrome-profile/` được thiết kế cho việc này. Admin mở trình duyệt do Playwright khởi chạy, đăng nhập Google, rồi đăng nhập Manus. Session sẽ được lưu trong profile đó.
    *   **Sử dụng Profile đã Setup:** Các endpoint crawl có thể sử dụng `profile_name` để tải profile đã đăng nhập.
    *   **Tự động hóa Đăng nhập Hoàn toàn (Khó khăn hơn):** Nếu không muốn setup thủ công, bạn cần tự động hóa việc điền form email/password Google, xử lý 2FA (nếu có). Việc này phức tạp và dễ bị Google thay đổi luồng.
*   **CAPTCHA:** Nếu gặp CAPTCHA, Playwright thuần túy sẽ không tự giải được. Cần dịch vụ bên thứ ba hoặc can thiệp thủ công.

### Tính Ổn định của Selectors
*   Các class CSS (đặc biệt là từ utility-first frameworks như Tailwind CSS mà Manus có vẻ sử dụng) có thể thay đổi thường xuyên.
*   **Ưu tiên:**
    *   `id` (nếu có và duy nhất).
    *   Các thuộc tính `data-*` (ví dụ: `data-testid`, `data-event-id`).
    *   Các selector dựa trên cấu trúc HTML và text ổn định (ví dụ: `button:has-text("Share")`).
*   Thường xuyên kiểm tra và cập nhật file `selectors.py`.

### Đạo đức Crawling
*   **`robots.txt`:** Luôn kiểm tra `https://manus.im/robots.txt` (hoặc trang web tương ứng) để tuân thủ các quy định.
*   **Tần suất Request:** Không gửi quá nhiều request trong thời gian ngắn. Thêm `await asyncio.sleep()` giữa các request hoặc các hành động nếu crawl liên tục nhiều trang/task.
*   **User-Agent:** Đặt một User-Agent tùy chỉnh và rõ ràng cho crawler của bạn. Playwright mặc định đã có User-Agent của trình duyệt nó điều khiển.
    ```python
    # Trong crawler.py, khi tạo context hoặc page
    # await page.set_extra_http_headers({"User-Agent": "MyManusCrawler/1.0 (+http://mycrawler.example.com)"})
    ```

### Bảo mật Endpoint Admin
*   Endpoint `/admin/setup-chrome-profile/` rất nhạy cảm vì nó khởi chạy trình duyệt có khả năng tương tác.
*   **Bắt buộc phải có xác thực mạnh** (OAuth2, JWT được khuyến nghị). Ví dụ API Key chỉ là minh họa cơ bản.
*   Giới hạn quyền truy cập chỉ cho người dùng quản trị.
*   Cân nhắc IP Whitelisting nếu có thể.

### Realtime WebSocket và Xử lý Nhiều Request
*   Giải pháp `ConnectionManager` với `request_id` trong URL WebSocket (`/ws/crawl-status/{request_id}`) cho phép mỗi client chỉ nhận cập nhật cho request mà nó quan tâm.
*   Client tạo `request_id` (ví dụ: UUID) và gửi nó khi gọi API HTTP crawl, đồng thời kết nối WebSocket tới endpoint có chứa `request_id` đó.
*   Cần đảm bảo dọn dẹp các `request_id` không còn client nào lắng nghe trong `ConnectionManager` để tránh memory leak.

### Quản lý Chrome Profile trong Docker
*   Sử dụng **Docker Volumes** (`chrome_profiles_data:/app/chrome_profiles` trong `docker-compose.yml`) để lưu trữ thư mục profile Chrome một cách bền bỉ, ngay cả khi container bị xóa và tạo lại.
*   Đường dẫn `/app/chrome_profiles` bên trong container được tham chiếu bởi biến môi trường `CHROME_PROFILE_BASE_PATH`.

### Chạy Playwright với Giao diện (UI) trong Docker
*   **Thách thức:** Để Playwright chạy ở chế độ non-headless (hiển thị UI) từ bên trong Docker container ra màn hình máy host, bạn cần cấu hình X11 forwarding.
    *   **Linux host:** Thường dễ cấu hình hơn.
    *   **macOS/Windows host:** Cần X server (ví dụ: XQuartz cho macOS, VcXsrv cho Windows) và cấu hình Docker để kết nối tới X server đó. `DISPLAY: host.docker.internal:0` là một cấu hình phổ biến cho Docker Desktop.
*   **Giải pháp thay thế:**
    *   **VNC:** Sử dụng Docker image có cài sẵn VNC server. Bạn kết nối vào container qua VNC client để xem và tương tác với UI.
    *   **Development Ngoài Docker:** Đối với việc setup profile hoặc debug UI, việc chạy ứng dụng FastAPI và Playwright trực tiếp trên máy host (không qua Docker) thường đơn giản hơn. Sau khi profile đã được setup và lưu vào thư mục được mount, bạn có thể chạy crawler trong Docker ở chế độ headless.
*   Image `mcr.microsoft.com/playwright/python:vX.Y.Z-jammy` đã cài sẵn các dependencies cần thiết cho trình duyệt chạy headless. Để chạy non-headless, bạn vẫn cần giải quyết vấn đề hiển thị.

### Xử lý Lỗi và Timeout
*   Sử dụng `try...except` để bắt các lỗi cụ thể của Playwright (`TimeoutError`, `Error`) và các lỗi mạng.
*   Cấu hình `timeout` hợp lý cho các thao tác Playwright (`page.goto`, `page.wait_for_selector`, etc.).
*   Cung cấp thông báo lỗi rõ ràng cho cả API response và WebSocket messages.

---

## 4. Xây dựng Frontend Đơn giản với Cập nhật Realtime

Một trang HTML đơn giản sử dụng JavaScript thuần để kết nối WebSocket và tương tác với API.

### Mục đích
*   Minh họa cách client có thể nhận cập nhật tiến trình crawl realtime.
*   Cung cấp giao diện cơ bản để kích hoạt crawl và xem kết quả.

### Nội dung file `templates/index.html`
Đặt file này trong thư mục `templates/`.
*(Nội dung file `index.html` như đã cung cấp ở câu trả lời trước, với JavaScript để tạo `request_id`, kết nối WebSocket tới `/ws/crawl-status/{request_id}`, và gọi API `/crawl-url-realtime/`.)*

```html
<!-- templates/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manus Crawler Realtime</title>
    <style>
        /* ... (CSS styles như đã cung cấp) ... */
    </style>
</head>
<body>
    <div id="container">
        <h1>Manus Crawler - Realtime Status</h1>
        <div>
            <label for="urlInput">URL để Crawl:</label>
            <input type="text" id="urlInput" value="https://manus.im/" size="50">
        </div>
        <div>
            <label for="profileNameInput">Tên Profile (tùy chọn):</label>
            <input type="text" id="profileNameInput" placeholder="my_manus_profile">
        </div>
        <div>
            <input type="checkbox" id="headlessCheckbox" checked>
            <label for="headlessCheckbox">Chạy Headless</label>
        </div>
         <div>
            <label for="requestIdInput">Request ID (tự động tạo):</label>
            <input type="text" id="requestIdInput" readonly size="36">
        </div>
        <button onclick="startCrawl()">Bắt đầu Crawl Realtime</button>
        
        <h2>Trạng thái Crawl:</h2>
        <div id="statusArea">Chưa có hoạt động...</div>

        <h2>Kết quả (JSON):</h2>
        <pre id="resultArea">Chưa có dữ liệu...</pre>
    </div>

    <script>
        // ... (JavaScript logic như đã cung cấp ở câu trả lời trước) ...
        // Bao gồm generateUUID(), connectWebSocket(requestId), logStatus(), startCrawl()
    </script>
</body>
</html>
```
Trong `main.py`, bạn cần một endpoint để phục vụ file HTML này (nếu chưa có):
```python
# main.py
# ...
from fastapi.responses import FileResponse

# ...
@app.get("/ui", response_class=HTMLResponse, summary="Giao diện người dùng Realtime")
async def get_realtime_ui_page():
    """Phục vụ trang HTML cho giao diện realtime."""
    html_file_path = "templates/index.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file index.html")
```

### Cách JavaScript hoạt động
1.  Khi người dùng nhấn "Bắt đầu Crawl Realtime":
    *   Một `request_id` (UUID) duy nhất được tạo phía client.
    *   `requestIdInput` được cập nhật để hiển thị ID này.
    *   `connectWebSocket(newRequestId)` được gọi, thiết lập kết nối WebSocket tới `/ws/crawl-status/{newRequestId}`.
    *   Một yêu cầu HTTP POST được gửi đến `/crawl-url-realtime/` (hoặc endpoint tương tự), mang theo `url`, `profile_name`, `headless`, và `request_id` trong body.
2.  **Server (FastAPI):**
    *   Endpoint HTTP nhận yêu cầu, trích xuất `request_id`.
    *   Gọi hàm `crawl_manus_page_content` với `websocket_callback` là `manager.send_to_request_id` và `request_id` tương ứng.
    *   Hàm crawl gửi các thông điệp tiến trình (`{"type": "progress", "message": "..."}`) và dữ liệu cuối cùng (`{"type": "data", "data": ...}`) hoặc lỗi (`{"type": "error", "message": "..."}`) thông qua `manager.send_to_request_id`.
3.  **Client (JavaScript):**
    *   `socket.onmessage` xử lý các tin nhắn từ WebSocket:
        *   Nếu `type` là "progress", hiển thị thông điệp tiến trình lên `statusArea`.
        *   Nếu `type` là "data", hiển thị dữ liệu JSON cuối cùng lên `resultArea`.
        *   Nếu `type` là "error", hiển thị thông báo lỗi.

---

Tài liệu này đã được sắp xếp lại và bổ sung các chi tiết quan trọng. Hãy sử dụng nó làm cơ sở để xây dựng và tinh chỉnh ứng dụng của bạn. Chúc may mắn!
