<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Admin Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        #container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #555;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input[type="text"], input[type="url"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        button {
            background-color: #dc3545;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        button:hover {
            background-color: #c82333;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        button.secondary {
            background-color: #6c757d;
        }

        button.secondary:hover {
            background-color: #5a6268;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        #statusArea {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .profile-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }

        .profile-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .profile-item:last-child {
            border-bottom: none;
        }

        .profile-name {
            font-weight: bold;
            color: #333;
        }

        .profile-actions {
            display: flex;
            gap: 10px;
        }

        .profile-actions button {
            padding: 5px 10px;
            font-size: 12px;
            margin: 0;
        }

        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .warning-box h3 {
            color: #856404;
            margin-top: 0;
        }

        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }

        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>🔐 Manus Crawler - Admin Panel</h1>

        <div class="nav-links">
            <a href="/ui">🏠 Main Interface</a>
            <a href="/docs">📚 API Docs</a>
            <a href="/health">💚 Health Check</a>
        </div>

        <div class="warning-box">
            <h3>⚠️ Cảnh báo bảo mật</h3>
            <p>Đây là khu vực admin. Chỉ người có quyền quản trị mới được truy cập.</p>
            <p>Việc setup Chrome profile sẽ mở trình duyệt thực để bạn đăng nhập thủ công vào Manus.im</p>
        </div>

        <h2>🎭 Chrome Profile Management</h2>

        <div class="form-group">
            <label for="apiKeyInput">API Key (bắt buộc):</label>
            <input type="password" id="apiKeyInput" placeholder="Nhập API key admin">
        </div>

        <div class="form-group">
            <label for="profileNameInput">Tên Profile:</label>
            <input type="text" id="profileNameInput" placeholder="my_manus_profile" value="manus_login_profile">
        </div>

        <div class="form-group">
            <label for="urlInput">URL để mở (mặc định Manus.im):</label>
            <input type="url" id="urlInput" value="https://manus.im/">
        </div>

        <button onclick="setupProfile()" id="setupBtn">🚀 Setup Chrome Profile</button>
        <button onclick="listProfiles()" id="listBtn" class="secondary">📋 List Profiles</button>
        <button onclick="clearLogs()" id="clearBtn" class="secondary">🗑️ Clear Logs</button>

        <h2>📊 Setup Status:</h2>
        <div id="statusArea">Chưa có hoạt động...</div>

        <div id="profilesList" class="profile-list" style="display: none;">
            <h3>📁 Chrome Profiles:</h3>
            <div id="profilesContent"></div>
        </div>
    </div>

    <script>
        // Log status message
        function logStatus(message) {
            const statusArea = document.getElementById('statusArea');
            const timestamp = new Date().toLocaleTimeString();
            statusArea.textContent += `[${timestamp}] ${message}\n`;
            statusArea.scrollTop = statusArea.scrollHeight;
        }

        // Show alert
        function showAlert(message, type = 'info') {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;

            const container = document.getElementById('container');
            container.insertBefore(alertDiv, container.firstChild.nextSibling);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Setup Chrome Profile
        async function setupProfile() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            const profileName = document.getElementById('profileNameInput').value.trim();
            const url = document.getElementById('urlInput').value.trim();

            if (!apiKey) {
                showAlert('Vui lòng nhập API Key!', 'danger');
                return;
            }

            if (!profileName) {
                showAlert('Vui lòng nhập tên profile!', 'danger');
                return;
            }

            if (!url) {
                showAlert('Vui lòng nhập URL!', 'danger');
                return;
            }

            // Disable button
            const setupBtn = document.getElementById('setupBtn');
            setupBtn.disabled = true;
            setupBtn.textContent = '⏳ Đang setup...';

            logStatus(`🚀 Starting profile setup for: ${profileName}`);
            logStatus(`📱 URL: ${url}`);
            logStatus(`⚠️  Browser sẽ mở trong vài giây. Hãy đăng nhập thủ công!`);

            try {
                const response = await fetch('/admin/setup-chrome-profile/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-KEY': apiKey
                    },
                    body: JSON.stringify({
                        profile_name: profileName,
                        url: url
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    if (result.success) {
                        logStatus(`✅ Setup thành công!`);
                        logStatus(`📄 Final URL: ${result.final_url}`);
                        showAlert(`Profile '${profileName}' đã được setup thành công!`, 'success');
                    } else {
                        logStatus(`❌ Setup thất bại: ${result.message}`);
                        showAlert(`Setup thất bại: ${result.message}`, 'danger');
                    }
                } else {
                    if (response.status === 401) {
                        logStatus(`❌ API Key không hợp lệ!`);
                        showAlert('API Key không hợp lệ!', 'danger');
                    } else {
                        logStatus(`❌ HTTP Error: ${response.status}`);
                        showAlert(`HTTP Error: ${response.status}`, 'danger');
                    }
                }

            } catch (error) {
                logStatus(`❌ Network error: ${error.message}`);
                showAlert(`Network error: ${error.message}`, 'danger');
            } finally {
                // Re-enable button
                setupBtn.disabled = false;
                setupBtn.textContent = '🚀 Setup Chrome Profile';
            }
        }

        // List Chrome Profiles
        async function listProfiles() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();

            if (!apiKey) {
                showAlert('Vui lòng nhập API Key để list profiles!', 'danger');
                return;
            }

            logStatus(`📋 Listing Chrome profiles...`);

            try {
                const response = await fetch('/admin/list-profiles/', {
                    method: 'GET',
                    headers: {
                        'X-API-KEY': apiKey
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    const profilesList = document.getElementById('profilesList');
                    const profilesContent = document.getElementById('profilesContent');

                    profilesContent.innerHTML = '';

                    if (result.profiles.length === 0) {
                        profilesContent.innerHTML = '<p>Không có profile nào được tìm thấy.</p>';
                    } else {
                        result.profiles.forEach(profile => {
                            const profileDiv = document.createElement('div');
                            profileDiv.className = 'profile-item';
                            profileDiv.innerHTML = `
                                <div>
                                    <div class="profile-name">${profile.name}</div>
                                    <small>Created: ${profile.created} | Size: ${profile.size}</small>
                                </div>
                                <div class="profile-actions">
                                    <button onclick="deleteProfile('${profile.name}')" style="background-color: #dc3545;">🗑️ Delete</button>
                                    <button onclick="testProfile('${profile.name}')" style="background-color: #28a745;">🧪 Test</button>
                                </div>
                            `;
                            profilesContent.appendChild(profileDiv);
                        });
                    }

                    profilesList.style.display = 'block';
                    logStatus(`✅ Found ${result.profiles.length} profiles`);
                    logStatus(`📁 Base path: ${result.base_path}`);

                } else {
                    if (response.status === 401) {
                        logStatus(`❌ API Key không hợp lệ!`);
                        showAlert('API Key không hợp lệ!', 'danger');
                    } else {
                        logStatus(`❌ Error: ${result.error || 'Unknown error'}`);
                        showAlert(`Error: ${result.error || 'Unknown error'}`, 'danger');
                    }
                }

            } catch (error) {
                logStatus(`❌ Network error: ${error.message}`);
                showAlert(`Network error: ${error.message}`, 'danger');
            }
        }

        // Delete profile
        async function deleteProfile(profileName) {
            if (!confirm(`Bạn có chắc muốn xóa profile '${profileName}'?\n\nHành động này không thể hoàn tác!`)) {
                return;
            }

            const apiKey = document.getElementById('apiKeyInput').value.trim();

            if (!apiKey) {
                showAlert('Vui lòng nhập API Key để xóa profile!', 'danger');
                return;
            }

            logStatus(`🗑️ Deleting profile: ${profileName}`);

            try {
                const response = await fetch(`/admin/delete-profile/${profileName}`, {
                    method: 'DELETE',
                    headers: {
                        'X-API-KEY': apiKey
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    logStatus(`✅ Profile '${profileName}' đã được xóa thành công`);
                    showAlert(`Profile '${profileName}' đã được xóa thành công`, 'success');
                    // Refresh list
                    listProfiles();
                } else {
                    if (response.status === 401) {
                        logStatus(`❌ API Key không hợp lệ!`);
                        showAlert('API Key không hợp lệ!', 'danger');
                    } else {
                        logStatus(`❌ Error: ${result.message || 'Unknown error'}`);
                        showAlert(`Error: ${result.message || 'Unknown error'}`, 'danger');
                    }
                }

            } catch (error) {
                logStatus(`❌ Network error: ${error.message}`);
                showAlert(`Network error: ${error.message}`, 'danger');
            }
        }

        // Test profile
        async function testProfile(profileName) {
            logStatus(`🧪 Testing profile: ${profileName}`);

            try {
                // Tạo request ID cho test
                const requestId = generateUUID();

                // Test bằng cách crawl HTML đơn giản với profile
                const testData = {
                    html_content: '<html><head><title>Test Profile</title></head><body><h1>Profile Test</h1></body></html>',
                    request_id: requestId,
                    profile_name: profileName,
                    headless: true
                };

                const response = await fetch('/crawl-html-realtime/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();

                if (response.ok) {
                    logStatus(`✅ Profile '${profileName}' test started successfully`);
                    logStatus(`📋 Request ID: ${requestId}`);
                    showAlert(`Profile '${profileName}' test started. Check main interface for results.`, 'success');
                } else {
                    logStatus(`❌ Profile test failed: ${response.status}`);
                    showAlert(`Profile test failed: ${response.status}`, 'danger');
                }

            } catch (error) {
                logStatus(`❌ Profile test error: ${error.message}`);
                showAlert(`Profile test error: ${error.message}`, 'danger');
            }
        }

        // Generate UUID for request ID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // Clear logs
        function clearLogs() {
            document.getElementById('statusArea').textContent = 'Chưa có hoạt động...';
            document.getElementById('profilesList').style.display = 'none';
        }

        // Initialize page
        window.onload = function() {
            logStatus('🔐 Admin panel loaded');
            logStatus('💡 Nhập API key và thông tin profile để bắt đầu');

            // Set default API key from environment (for development)
            const defaultApiKey = 'your_super_secret_key_here'; // This should come from config
            document.getElementById('apiKeyInput').placeholder = `Default: ${defaultApiKey}`;
        };
    </script>
</body>
</html>
