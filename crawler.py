import os
import asyncio
import platform
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Awaitable
from playwright.async_api import async_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
import manus_selectors as sel

# Đường dẫn cơ sở cho Chrome profiles
CHROME_PROFILE_BASE_PATH = os.getenv("CHROME_PROFILE_BASE_PATH", "./chrome_profiles")

def get_system_chrome_user_data_dir() -> Optional[str]:
    """L<PERSON>y đường dẫn User Data Directory của Chrome hệ thống."""
    system = platform.system()
    home = Path.home()

    if system == "Windows":
        return str(home / "AppData" / "Local" / "Google" / "Chrome" / "User Data")
    elif system == "Darwin":  # macOS
        return str(home / "Library" / "Application Support" / "Google" / "Chrome")
    elif system == "Linux":
        return str(home / ".config" / "google-chrome")
    else:
        return None

def cleanup_profile_locks(user_data_dir: str):
    """Xóa các lock files để tránh lỗi SingletonLock."""
    if user_data_dir and os.path.exists(user_data_dir):
        lock_files = [
            os.path.join(user_data_dir, "SingletonLock"),
            os.path.join(user_data_dir, "lockfile"),
            os.path.join(user_data_dir, "SingletonSocket"),
            os.path.join(user_data_dir, "SingletonCookie")
        ]

        for lock_file in lock_files:
            try:
                if os.path.exists(lock_file):
                    os.remove(lock_file)
            except Exception:
                pass  # Ignore errors when removing lock files

def kill_chrome_processes_for_profile(profile_name: str):
    """Kill Chrome processes sử dụng profile cụ thể."""
    import subprocess

    try:
        # Kill Chrome processes với profile name
        subprocess.run([
            "pkill", "-f", f"chrome.*{profile_name}"
        ], capture_output=True)

        subprocess.run([
            "pkill", "-f", f"Chromium.*{profile_name}"
        ], capture_output=True)

        # Đợi một chút để processes terminate
        import time
        time.sleep(0.5)

    except Exception:
        pass  # Ignore errors when killing processes

async def launch_browser_with_profile(
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = True
) -> tuple[Browser, BrowserContext]:
    """
    Khởi chạy browser với profile được chỉ định.

    Args:
        profile_name: Tên profile tùy chỉnh
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless

    Returns:
        Tuple của (browser, context)
    """
    playwright = await async_playwright().start()

    if use_system_profile:
        user_data_dir = get_system_chrome_user_data_dir()
        if not user_data_dir or not os.path.exists(user_data_dir):
            raise ValueError("Không tìm thấy Chrome profile hệ thống")
    elif profile_name:
        user_data_dir = os.path.join(CHROME_PROFILE_BASE_PATH, profile_name)
        os.makedirs(user_data_dir, exist_ok=True)
    else:
        user_data_dir = None

    # Cleanup lock files và kill processes trước khi launch
    if user_data_dir and profile_name:
        kill_chrome_processes_for_profile(profile_name)
        cleanup_profile_locks(user_data_dir)

    try:
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=headless,
            args=[
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--no-first-run",
                "--disable-default-apps"
            ]
        )
    except Exception as e:
        # Nếu vẫn lỗi, thử cleanup lần nữa và retry
        if "SingletonLock" in str(e) or "ProcessSingleton" in str(e):
            if user_data_dir and profile_name:
                kill_chrome_processes_for_profile(profile_name)
                cleanup_profile_locks(user_data_dir)
                # Đợi một chút rồi thử lại
                await asyncio.sleep(2)
                browser = await playwright.chromium.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    headless=headless,
                    args=[
                        "--no-sandbox",
                        "--disable-blink-features=AutomationControlled",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--disable-dev-shm-usage",
                        "--disable-extensions",
                        "--no-first-run",
                        "--disable-default-apps"
                    ]
                )
            else:
                raise e
        else:
            raise e

    return playwright, browser

async def setup_chrome_profile_interactive(profile_name: str, url: str = "https://manus.im/") -> Dict[str, Any]:
    """
    Thiết lập Chrome profile bằng cách mở trình duyệt non-headless
    để người dùng đăng nhập thủ công.

    Args:
        profile_name: Tên profile để tạo/sử dụng
        url: URL để mở (mặc định là Manus.im)

    Returns:
        Dictionary chứa thông tin về việc setup profile
    """
    try:
        playwright, context = await launch_browser_with_profile(
            profile_name=profile_name,
            headless=False  # Non-headless để người dùng tương tác
        )

        page = await context.new_page()
        await page.goto(url)

        # Chờ người dùng đăng nhập (có thể điều chỉnh thời gian)
        await page.wait_for_timeout(60000)  # 60 giây

        # Kiểm tra xem có đăng nhập thành công không
        current_url = page.url

        await context.close()
        await playwright.stop()

        return {
            "success": True,
            "profile_name": profile_name,
            "final_url": current_url,
            "message": f"Profile '{profile_name}' đã được thiết lập"
        }

    except Exception as e:
        return {
            "success": False,
            "profile_name": profile_name,
            "error": str(e),
            "message": f"Lỗi khi thiết lập profile '{profile_name}': {str(e)}"
        }

async def crawl_manus_page_content(
    html_content: Optional[str] = None,
    url: Optional[str] = None,
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = True,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Crawl nội dung từ trang Manus.im hoặc parse HTML tĩnh.

    Args:
        html_content: Nội dung HTML tĩnh để parse
        url: URL để crawl (nếu không có html_content)
        profile_name: Tên profile Chrome để sử dụng
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless
        websocket_callback: Callback để gửi cập nhật realtime
        request_id: ID của request để tracking

    Returns:
        Dictionary chứa dữ liệu đã crawl
    """

    async def send_progress(message: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, message)

    try:
        await send_progress("Bắt đầu khởi chạy trình duyệt...")

        playwright, context = await launch_browser_with_profile(
            profile_name=profile_name,
            use_system_profile=use_system_profile,
            headless=headless
        )

        page = await context.new_page()

        if html_content:
            await send_progress("Đang load HTML content...")
            await page.set_content(html_content)
        elif url:
            await send_progress(f"Đang truy cập URL: {url}")
            await page.goto(url, wait_until="networkidle")
        else:
            raise ValueError("Cần cung cấp html_content hoặc url")

        await send_progress("Đang trích xuất dữ liệu...")

        # Trích xuất dữ liệu theo selectors
        data = {
            "page_title": "",
            "tasks": [],
            "current_task_title": "",
            "chat_messages": [],
            "footer_user": {},
            "profile_status": {
                "profile_name": profile_name,
                "use_system_profile": use_system_profile,
                "headless": headless
            }
        }

        # Lấy title trang
        try:
            data["page_title"] = await page.title()
        except:
            pass

        # Lấy danh sách tasks từ sidebar
        await send_progress("Đang lấy danh sách tasks...")
        try:
            task_containers = await page.query_selector_all(sel.TASK_ITEM_CONTAINER_CSS)
            for container in task_containers:
                task_data = {}

                # Icon
                try:
                    icon_img = await container.query_selector(sel.TASK_ITEM_ICON_IMG_CSS)
                    if icon_img:
                        task_data["icon_src"] = await icon_img.get_attribute("src")
                except:
                    pass

                # Title
                try:
                    title_span = await container.query_selector(sel.TASK_ITEM_TITLE_SPAN_CSS)
                    if title_span:
                        task_data["title"] = await title_span.get_attribute("title")
                        task_data["title_text"] = await title_span.inner_text()
                except:
                    pass

                # Timestamp
                try:
                    timestamp_span = await container.query_selector(sel.TASK_ITEM_TIMESTAMP_CSS)
                    if timestamp_span:
                        task_data["timestamp"] = await timestamp_span.inner_text()
                except:
                    pass

                # Preview
                try:
                    preview_span = await container.query_selector(sel.TASK_ITEM_PREVIEW_SPAN_CSS)
                    if preview_span:
                        task_data["preview"] = await preview_span.get_attribute("title")
                except:
                    pass

                if task_data:
                    data["tasks"].append(task_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy tasks: {str(e)}")

        # Lấy tiêu đề task hiện tại
        try:
            current_title_elem = await page.query_selector(sel.CURRENT_TASK_TITLE_MAIN_CSS)
            if current_title_elem:
                data["current_task_title"] = await current_title_elem.inner_text()
        except:
            pass

        # Lấy tin nhắn chat
        await send_progress("Đang lấy tin nhắn chat...")
        try:
            chat_events = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
            for event in chat_events:
                message_data = {
                    "event_id": await event.get_attribute("data-event-id")
                }

                # Tin nhắn user
                try:
                    user_msg = await event.query_selector(sel.USER_MESSAGE_TEXT_CSS)
                    if user_msg:
                        message_data["user_message"] = await user_msg.inner_text()
                        message_data["type"] = "user"
                except:
                    pass

                # Tin nhắn Manus
                try:
                    manus_msg = await event.query_selector(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)
                    if manus_msg:
                        message_data["manus_message"] = await manus_msg.inner_text()
                        message_data["manus_html"] = await manus_msg.inner_html()
                        message_data["type"] = "manus"
                except:
                    pass

                # Timestamp
                try:
                    timestamp_elem = await event.query_selector(sel.MESSAGE_TIMESTAMP_CSS)
                    if timestamp_elem:
                        message_data["timestamp"] = await timestamp_elem.inner_text()
                except:
                    pass

                if "user_message" in message_data or "manus_message" in message_data:
                    data["chat_messages"].append(message_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy chat messages: {str(e)}")

        # Lấy thông tin user ở footer
        try:
            footer_avatar = await page.query_selector(sel.FOOTER_USER_AVATAR_IMG_CSS)
            footer_name = await page.query_selector(sel.FOOTER_USER_NAME_SPAN_CSS)

            if footer_avatar or footer_name:
                data["footer_user"] = {
                    "avatar_src": await footer_avatar.get_attribute("src") if footer_avatar else None,
                    "name": await footer_name.inner_text() if footer_name else None
                }
        except:
            pass

        await send_progress("Hoàn thành crawl dữ liệu!")

        await context.close()
        await playwright.stop()

        return {
            "success": True,
            "data": data,
            "message": "Crawl thành công"
        }

    except Exception as e:
        await send_progress(f"Lỗi: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi crawl: {str(e)}"
        }
