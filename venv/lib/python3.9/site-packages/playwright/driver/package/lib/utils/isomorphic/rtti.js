"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var rtti_exports = {};
__export(rtti_exports, {
  isError: () => isError,
  isObject: () => isObject,
  isRegExp: () => isRegExp,
  isString: () => import_stringUtils.isString
});
module.exports = __toCommonJS(rtti_exports);
var import_stringUtils = require("./stringUtils");
function isRegExp(obj) {
  return obj instanceof RegExp || Object.prototype.toString.call(obj) === "[object RegExp]";
}
function isObject(obj) {
  return typeof obj === "object" && obj !== null;
}
function isError(obj) {
  return obj instanceof Error || obj && Object.getPrototypeOf(obj)?.name === "Error";
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  isError,
  isObject,
  isRegExp,
  isString
});
