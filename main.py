import os
import asyncio
import uuid
from typing import Op<PERSON>, Dict, Any, List
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends, Header
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import json
from dotenv import load_dotenv

from crawler import crawl_manus_page_content, setup_chrome_profile_interactive

# Load environment variables
load_dotenv()

# Khởi tạo FastAPI app
app = FastAPI(
    title="Manus Crawler API",
    description="API để crawl dữ liệu từ Manus.im với Playwright và realtime updates",
    version="1.0.0"
)

# Pydantic Models
class TaskItem(BaseModel):
    icon_src: Optional[str] = None
    title: Optional[str] = None
    title_text: Optional[str] = None
    timestamp: Optional[str] = None
    preview: Optional[str] = None

class ChatMessage(BaseModel):
    event_id: Optional[str] = None
    type: Optional[str] = None
    user_message: Optional[str] = None
    manus_message: Optional[str] = None
    manus_html: Optional[str] = None
    timestamp: Optional[str] = None

class FooterUserInfo(BaseModel):
    avatar_src: Optional[str] = None
    name: Optional[str] = None

class ProfileStatus(BaseModel):
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawledDataResponse(BaseModel):
    page_title: str = ""
    tasks: List[TaskItem] = []
    current_task_title: str = ""
    chat_messages: List[ChatMessage] = []
    footer_user: FooterUserInfo = FooterUserInfo()
    profile_status: ProfileStatus = ProfileStatus()

class CrawlUrlRequest(BaseModel):
    url: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlUrlRequestWithId(CrawlUrlRequest):
    request_id: str

class CrawlHtmlRequest(BaseModel):
    html_content: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlHtmlRequestWithId(CrawlHtmlRequest):
    request_id: str

class SetupProfileRequest(BaseModel):
    profile_name: str
    url: str = "https://manus.im/"

class WebSocketMessage(BaseModel):
    type: str  # "progress", "data", "error"
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, request_id: str):
        await websocket.accept()
        if request_id not in self.active_connections:
            self.active_connections[request_id] = []
        self.active_connections[request_id].append(websocket)

    def disconnect(self, websocket: WebSocket, request_id: str):
        if request_id in self.active_connections:
            if websocket in self.active_connections[request_id]:
                self.active_connections[request_id].remove(websocket)
            if not self.active_connections[request_id]:
                del self.active_connections[request_id]

    async def send_to_request_id(self, request_id: str, message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="progress", message=message)
            message_json = websocket_message.model_dump_json()

            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)

            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_data_to_request_id(self, request_id: str, data: Dict[str, Any]):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="data", data=data)
            message_json = websocket_message.model_dump_json()

            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)

            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_error_to_request_id(self, request_id: str, error_message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="error", message=error_message)
            message_json = websocket_message.model_dump_json()

            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)

            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

manager = ConnectionManager()

# API Key validation
def verify_api_key(x_api_key: Optional[str] = Header(None)):
    expected_key = os.getenv("ADMIN_API_KEY", "your_super_secret_key_here")
    if x_api_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API Key")
    return x_api_key

# WebSocket endpoint
@app.websocket("/ws/crawl-status/{request_id}")
async def websocket_endpoint(websocket: WebSocket, request_id: str):
    await manager.connect(websocket, request_id)
    try:
        while True:
            # Giữ kết nối mở
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, request_id)

# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def root():
    return """
    <html>
        <head>
            <title>Manus Crawler API</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
                .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
                .admin-card { border-left-color: #dc3545; }
                a { color: #007bff; text-decoration: none; font-weight: bold; }
                a:hover { text-decoration: underline; }
                .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
            </style>
        </head>
        <body>
            <h1>🕷️ Manus Crawler API</h1>
            <p>Ứng dụng FastAPI để crawl dữ liệu từ Manus.im với Playwright và realtime updates</p>

            <div class="card">
                <h3>👤 User Interface</h3>
                <p>Giao diện chính để crawl dữ liệu với realtime updates</p>
                <a href="/ui">🚀 Mở Giao diện Crawl</a>
            </div>

            <div class="card admin-card">
                <h3>🔐 Admin Panel</h3>
                <p>Quản lý Chrome profiles và cấu hình hệ thống (cần API key)</p>
                <a href="/admin">⚙️ Mở Admin Panel</a>
            </div>

            <div class="card">
                <h3>📚 API Documentation</h3>
                <p>Swagger UI với tất cả endpoints và schemas</p>
                <a href="/docs">📖 Xem API Docs</a>
            </div>

            <div class="card">
                <h3>💚 Health Check</h3>
                <p>Kiểm tra trạng thái server</p>
                <a href="/health">🔍 Health Status</a>
            </div>

            <div class="card warning">
                <h3>⚠️ Lưu ý</h3>
                <ul>
                    <li>Admin panel yêu cầu API key để truy cập</li>
                    <li>Setup Chrome profile cần tương tác thủ công</li>
                    <li>Tuân thủ robots.txt và terms of service</li>
                </ul>
            </div>
        </body>
    </html>
    """

@app.post("/crawl-url/", response_model=Dict[str, Any])
async def crawl_url(request: CrawlUrlRequest):
    """Crawl URL và trả về dữ liệu (không realtime)."""
    result = await crawl_manus_page_content(
        url=request.url,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless
    )
    return result

@app.post("/crawl-url-realtime/", response_model=Dict[str, Any])
async def crawl_url_realtime(request: CrawlUrlRequestWithId):
    """Crawl URL với cập nhật realtime qua WebSocket."""

    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)

    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            url=request.url,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )

        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))

        return result

    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())

    return {"message": "Crawl started", "request_id": request.request_id}

@app.post("/crawl-html/", response_model=Dict[str, Any])
async def crawl_html(request: CrawlHtmlRequest):
    """Parse HTML content và trả về dữ liệu (không realtime)."""
    result = await crawl_manus_page_content(
        html_content=request.html_content,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless
    )
    return result

@app.post("/crawl-html-realtime/", response_model=Dict[str, Any])
async def crawl_html_realtime(request: CrawlHtmlRequestWithId):
    """Parse HTML content với cập nhật realtime qua WebSocket."""

    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)

    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            html_content=request.html_content,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )

        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))

        return result

    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())

    return {"message": "HTML parsing started", "request_id": request.request_id}

@app.post("/admin/setup-chrome-profile/", dependencies=[Depends(verify_api_key)])
async def admin_setup_chrome_profile(request: SetupProfileRequest):
    """
    Endpoint admin để thiết lập Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    result = await setup_chrome_profile_interactive(
        profile_name=request.profile_name,
        url=request.url
    )
    return result

@app.get("/admin/list-profiles/", dependencies=[Depends(verify_api_key)])
async def admin_list_profiles():
    """
    Endpoint admin để liệt kê các Chrome profiles.
    Yêu cầu API Key trong header X-API-KEY.
    """
    import os
    from pathlib import Path
    from datetime import datetime

    chrome_profiles_path = os.getenv("CHROME_PROFILE_BASE_PATH", "./chrome_profiles")
    profiles = []

    try:
        if os.path.exists(chrome_profiles_path):
            for item in os.listdir(chrome_profiles_path):
                item_path = os.path.join(chrome_profiles_path, item)
                if os.path.isdir(item_path):
                    # Lấy thông tin profile
                    stat = os.stat(item_path)
                    created_time = datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M")

                    # Tính size thư mục (đơn giản)
                    total_size = 0
                    try:
                        for dirpath, dirnames, filenames in os.walk(item_path):
                            for filename in filenames:
                                filepath = os.path.join(dirpath, filename)
                                if os.path.exists(filepath):
                                    total_size += os.path.getsize(filepath)
                    except:
                        total_size = 0

                    # Chuyển đổi size sang MB
                    size_mb = round(total_size / (1024 * 1024), 1)

                    profiles.append({
                        "name": item,
                        "created": created_time,
                        "size": f"{size_mb}MB",
                        "path": item_path
                    })

        return {
            "success": True,
            "profiles": profiles,
            "total": len(profiles),
            "base_path": chrome_profiles_path
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "profiles": [],
            "total": 0
        }

@app.delete("/admin/delete-profile/{profile_name}", dependencies=[Depends(verify_api_key)])
async def admin_delete_profile(profile_name: str):
    """
    Endpoint admin để xóa Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    import shutil

    chrome_profiles_path = os.getenv("CHROME_PROFILE_BASE_PATH", "./chrome_profiles")
    profile_path = os.path.join(chrome_profiles_path, profile_name)

    try:
        if os.path.exists(profile_path) and os.path.isdir(profile_path):
            shutil.rmtree(profile_path)
            return {
                "success": True,
                "message": f"Profile '{profile_name}' đã được xóa thành công",
                "profile_name": profile_name
            }
        else:
            return {
                "success": False,
                "message": f"Profile '{profile_name}' không tồn tại",
                "profile_name": profile_name
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi xóa profile '{profile_name}': {str(e)}",
            "profile_name": profile_name
        }

@app.get("/ui", response_class=HTMLResponse)
async def get_realtime_ui_page():
    """Phục vụ trang HTML cho giao diện realtime."""
    html_file_path = "templates/index.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file index.html")

@app.get("/admin", response_class=HTMLResponse)
async def get_admin_page():
    """Phục vụ trang HTML cho admin panel."""
    html_file_path = "templates/admin.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file admin.html")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "Manus Crawler API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
