#!/usr/bin/env python3
"""
Test WebSocket connection
"""

import asyncio
import websockets
import json
import uuid

async def test_websocket():
    """Test WebSocket connection to the server."""
    
    # Tạo request ID
    request_id = str(uuid.uuid4())
    print(f"🔗 Testing WebSocket with request_id: {request_id}")
    
    # WebSocket URL
    ws_url = f"ws://localhost:8000/ws/crawl-status/{request_id}"
    print(f"📡 Connecting to: {ws_url}")
    
    try:
        # Kết nối WebSocket
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Gửi một message test
            test_message = {"type": "test", "message": "Hello from test client"}
            await websocket.send(json.dumps(test_message))
            print("📤 Sent test message")
            
            # Chờ response (timeout 5 giây)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Received: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received (this is normal for our WebSocket)")
            
            print("🎉 WebSocket test completed successfully!")
            
    except Exception as e:
        print(f"❌ WebSocket connection failed: {str(e)}")
        return False
    
    return True

async def test_api_endpoint():
    """Test API endpoint."""
    import httpx
    
    print("\n🧪 Testing API endpoint...")
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            response = await client.get("http://localhost:8000/health")
            if response.status_code == 200:
                print("✅ Health endpoint working")
                print(f"   Response: {response.json()}")
            else:
                print(f"❌ Health endpoint failed: {response.status_code}")
                return False
            
            # Test crawl endpoint với HTML content
            request_id = str(uuid.uuid4())
            crawl_data = {
                "html_content": "<html><head><title>Test</title></head><body><h1>Test</h1></body></html>",
                "request_id": request_id,
                "headless": True
            }
            
            response = await client.post(
                "http://localhost:8000/crawl-html-realtime/",
                json=crawl_data
            )
            
            if response.status_code == 200:
                print("✅ Crawl endpoint working")
                print(f"   Response: {response.json()}")
            else:
                print(f"❌ Crawl endpoint failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")
        return False
    
    return True

async def main():
    """Main test function."""
    print("🚀 Manus Crawler - WebSocket & API Test")
    print("=" * 50)
    
    # Test API first
    api_success = await test_api_endpoint()
    
    # Test WebSocket
    ws_success = await test_websocket()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"   API Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"   WebSocket Test: {'✅ PASSED' if ws_success else '❌ FAILED'}")
    
    if api_success and ws_success:
        print("\n🎉 All tests passed! WebSocket should work in browser now.")
        print("\n💡 Try these steps:")
        print("   1. Open http://localhost:8000/ui in browser")
        print("   2. Hard refresh (Ctrl+F5 or Cmd+Shift+R)")
        print("   3. Open browser console (F12) to see any errors")
        print("   4. Click 'Bắt đầu Crawl Realtime' button")
    else:
        print("\n⚠️  Some tests failed. Check server logs.")

if __name__ == "__main__":
    asyncio.run(main())
